#!/usr/bin/env python3
"""
Transform processed funnel questions CSV to add parent categories and merge content
"""

import csv
import pandas as pd

def load_category_mapping(categories_file):
    """Load parent-child category mapping from blog categories file"""
    category_map = {}
    
    with open(categories_file, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            child_category = row['name']
            parent_category = row['blog_categories1'] if row['blog_categories1'] else child_category
            category_map[child_category] = parent_category
    
    return category_map

def transform_csv(input_file, categories_file, output_file):
    """Transform the processed CSV to new format"""
    
    # Load category mapping
    category_map = load_category_mapping(categories_file)
    
    # Read the processed questions CSV
    df = pd.read_csv(input_file)
    
    # Filter only suitable questions
    suitable_df = df[df['Is Suitable'] == 'Yes'].copy()
    
    # Create new columns
    suitable_df['parent_category'] = suitable_df['Category'].map(category_map)
    suitable_df['child_category'] = suitable_df['Category']
    
    # Merge reasoning and article concept into content_focus
    suitable_df['content_focus'] = suitable_df['Reasoning'] + ' | ' + suitable_df['Article Concept']
    
    # Select and rename columns for final output
    final_df = suitable_df[['Original Question', 'parent_category', 'child_category', 'content_focus']].copy()
    final_df.columns = ['original_question', 'parent_category', 'child_category', 'content_focus']
    
    # Handle any unmapped categories
    final_df['parent_category'] = final_df['parent_category'].fillna('Uncategorized')
    
    # Save to new CSV
    final_df.to_csv(output_file, index=False, encoding='utf-8')
    
    return final_df

def main():
    input_file = 'processed_funnel_questions.csv'
    categories_file = 'blog_categories (Default View).csv'
    output_file = 'transformed_funnel_questions.csv'
    
    print("Transforming CSV...")
    print(f"Input: {input_file}")
    print(f"Categories: {categories_file}")
    print(f"Output: {output_file}")
    
    try:
        result_df = transform_csv(input_file, categories_file, output_file)
        
        print(f"\n✅ Transformation complete!")
        print(f"Total suitable questions: {len(result_df)}")
        
        # Show parent category distribution
        print(f"\nParent category distribution:")
        parent_counts = result_df['parent_category'].value_counts()
        for parent, count in parent_counts.items():
            print(f"  {parent}: {count}")
        
        # Show sample of results
        print(f"\nSample of transformed data:")
        print(result_df.head(3).to_string(index=False))
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
