#!/usr/bin/env python3
"""
Bottom Funnel Question Processor

This script processes scraped Google PAA questions to:
1. Filter questions suitable for bottom/middle funnel content
2. Generate article concepts and reasoning
3. Categorize suitable questions using predefined categories

Usage:
    python funnel_question_processor.py [--test] [--test-count N]
"""

import csv
import json
import os
import argparse
from typing import List, Dict, Tuple, Optional
import requests
from dataclasses import dataclass
import time

@dataclass
class ProcessedQuestion:
    original_question: str
    is_suitable: bool
    reasoning: str
    article_concept: str
    category: Optional[str] = None

class LLMClient:
    def __init__(self, endpoint: str, model: str, api_key: str):
        self.endpoint = endpoint
        self.model = model
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def call_llm(self, prompt: str, system_prompt: str = None) -> str:
        """Make a call to the LLM API"""
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.3,
            "max_tokens": 1000
        }
        
        try:
            response = requests.post(self.endpoint + "/chat/completions", 
                                   headers=self.headers, 
                                   json=payload, 
                                   timeout=30)
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            print(f"Error calling LLM: {e}")
            return ""

class FunnelQuestionProcessor:
    def __init__(self, llm_client: LLMClient, business_context: str, categories: List[str]):
        self.llm_client = llm_client
        self.business_context = business_context
        self.categories = categories
    
    def evaluate_question_suitability(self, question: str) -> Tuple[bool, str, str]:
        """
        Evaluate if a question is suitable for funnel content creation
        Returns: (is_suitable, reasoning, article_concept)
        """
        system_prompt = f"""You are an expert B2B Content Strategist for AgentiveAI, a company that provides AI-powered chat agents and automation solutions for e-commerce businesses.

Business Context:
{self.business_context}

Your task is to analyze a question scraped from Google's "People Also Ask" and determine if it is suitable for creating a **Middle of the Funnel (MOFU)** or **Bottom of the Funnel (BOFU)** blog post.

A suitable question implies the searcher has a specific problem that AgentiveAI's solutions can solve. They are past the initial awareness stage and are actively looking for solutions or comparing options.

**Decision Criteria:**

1. **REJECT (Not Suitable):**
   * Very broad, academic, or definitional questions about general e-commerce or business
   * Questions that are too generic and don't relate to problems AgentiveAI solves
   * Questions clearly aimed at job seekers or students
   * Questions about unrelated industries or services

2. **ACCEPT (Suitable):**
   * Questions about chat support, customer service automation, or AI agents
   * Questions about conversion optimization, cart abandonment, or sales automation
   * Questions about e-commerce platform integrations (Shopify, WooCommerce)
   * Questions about customer support efficiency or 24/7 availability
   * Questions about lead generation, qualification, or sales processes
   * Questions that mention specific pain points AgentiveAI addresses

**Examples:**
- SUITABLE: "How to reduce cart abandonment with chat support?"
- SUITABLE: "What is the best AI chatbot for Shopify?"
- NOT SUITABLE: "What are the 5 C's of ecommerce?"
- NOT SUITABLE: "How to start an online business?"

Respond in this exact JSON format:
{{
    "is_suitable": true/false,
    "reasoning": "Brief explanation of why this question is or isn't suitable for funnel content",
    "article_concept": "If suitable, describe the article concept, angle, and how it would serve the funnel. If not suitable, write 'N/A'"
}}"""

        prompt = f"Evaluate this question for funnel content suitability: '{question}'"
        
        response = self.llm_client.call_llm(prompt, system_prompt)
        
        try:
            # Parse JSON response
            result = json.loads(response)
            return result["is_suitable"], result["reasoning"], result["article_concept"]
        except:
            # Fallback parsing if JSON fails
            return False, "Failed to parse LLM response", "N/A"
    
    def categorize_question(self, question: str, article_concept: str) -> str:
        """
        Categorize a suitable question into one of the predefined categories
        """
        categories_list = "\n".join([f"- {cat}" for cat in self.categories])
        
        system_prompt = f"""You are a content categorization expert for AgentiveAI.

Available Categories:
{categories_list}

Your task is to match the given question and article concept to exactly ONE category from the list above.

Respond with ONLY the category name exactly as it appears in the list. Do not add explanations or additional text."""

        prompt = f"""Question: '{question}'
Article Concept: '{article_concept}'

Which single category from the list best fits this content?"""
        
        response = self.llm_client.call_llm(prompt, system_prompt).strip()
        
        # Validate the response is in our categories list
        if response in self.categories:
            return response
        else:
            # Try to find a close match
            for category in self.categories:
                if category.lower() in response.lower() or response.lower() in category.lower():
                    return category
            return "Uncategorized"

def load_questions(file_path: str) -> List[str]:
    """Load questions from CSV file"""
    questions = []
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            if row and row[0].strip():  # Skip empty rows
                questions.append(row[0].strip())
    return questions

def load_business_context(file_path: str) -> str:
    """Load business context from text file"""
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.read()

def load_categories(file_path: str) -> List[str]:
    """Load categories from CSV file"""
    categories = []
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            if row and row[0].strip():
                categories.append(row[0].strip())
    return categories

def save_results(results: List[ProcessedQuestion], output_file: str):
    """Save processed results to CSV"""
    with open(output_file, 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['Original Question', 'Is Suitable', 'Reasoning', 'Article Concept', 'Category'])
        
        for result in results:
            writer.writerow([
                result.original_question,
                'Yes' if result.is_suitable else 'No',
                result.reasoning,
                result.article_concept,
                result.category or 'N/A'
            ])

def main():
    parser = argparse.ArgumentParser(description='Process bottom funnel questions')
    parser.add_argument('--test', action='store_true', help='Run in test mode')
    parser.add_argument('--test-count', type=int, default=10, help='Number of questions to process in test mode')
    args = parser.parse_args()
    
    # Load configuration
    llm_endpoint = os.getenv('LLM_ENDPOINT', 'https://llm.chutes.ai/v1')
    llm_model = os.getenv('LLM_MODEL', 'Qwen/Qwen3-235B-A22B-Instruct-2507')
    llm_api_key = os.getenv('LLM_API_KEY', 'cpk_bd1d37cd6978483584aaa12d47312bb4.28b956be4459531ca85abf7422c1572b.KcFjkwzarUI5nrvvX9FMVmOubx2LHwm4')
    
    # Initialize LLM client
    llm_client = LLMClient(llm_endpoint, llm_model, llm_api_key)
    
    # Load data files
    print("Loading data files...")
    questions = load_questions('Bottom Funnel Questions - Sheet1.csv')
    business_context = load_business_context('business_context.txt')
    categories = load_categories('Categories - Sheet1.csv')
    
    print(f"Loaded {len(questions)} questions")
    print(f"Loaded {len(categories)} categories")
    
    # Initialize processor
    processor = FunnelQuestionProcessor(llm_client, business_context, categories)
    
    # Determine how many questions to process
    if args.test:
        questions_to_process = questions[:args.test_count]
        output_file = f'test_results_{args.test_count}.csv'
        print(f"Running in test mode - processing {len(questions_to_process)} questions")
    else:
        questions_to_process = questions
        output_file = 'processed_funnel_questions.csv'
        print(f"Processing all {len(questions_to_process)} questions")
    
    results = []
    
    for i, question in enumerate(questions_to_process, 1):
        print(f"Processing question {i}/{len(questions_to_process)}: {question[:50]}...")
        
        # Step 1: Evaluate suitability
        is_suitable, reasoning, article_concept = processor.evaluate_question_suitability(question)
        
        result = ProcessedQuestion(
            original_question=question,
            is_suitable=is_suitable,
            reasoning=reasoning,
            article_concept=article_concept
        )
        
        # Step 2: If suitable, categorize
        if is_suitable:
            print(f"  ✓ Suitable - categorizing...")
            category = processor.categorize_question(question, article_concept)
            result.category = category
            print(f"  → Category: {category}")
        else:
            print(f"  ✗ Not suitable")
        
        results.append(result)
        
        # Add small delay to avoid overwhelming the API
        time.sleep(0.5)
    
    # Save results
    save_results(results, output_file)
    
    # Print summary
    suitable_count = sum(1 for r in results if r.is_suitable)
    print(f"\n=== PROCESSING COMPLETE ===")
    print(f"Total questions processed: {len(results)}")
    print(f"Suitable for funnel content: {suitable_count}")
    print(f"Not suitable: {len(results) - suitable_count}")
    print(f"Results saved to: {output_file}")
    
    if suitable_count > 0:
        print(f"\nCategory distribution:")
        category_counts = {}
        for result in results:
            if result.is_suitable and result.category:
                category_counts[result.category] = category_counts.get(result.category, 0) + 1
        
        for category, count in sorted(category_counts.items()):
            print(f"  {category}: {count}")

if __name__ == "__main__":
    main()
