#!/usr/bin/env python3
"""
Bottom Funnel Question Processor

This script processes scraped Google PAA questions to:
1. Filter questions suitable for bottom/middle funnel content
2. Generate article concepts and reasoning
3. Categorize suitable questions using predefined categories

Usage:
    python funnel_question_processor.py [--test] [--test-count N]
"""

import csv
import json
import os
import argparse
from typing import List, Dict, Tuple, Optional
import requests
from dataclasses import dataclass
import time
import pickle

@dataclass
class ProcessedQuestion:
    original_question: str
    is_suitable: bool
    reasoning: str
    article_concept: str
    category: Optional[str] = None

class LLMClient:
    def __init__(self, endpoint: str, model: str, api_key: str):
        self.endpoint = endpoint
        self.model = model
        self.api_key = api_key
        # For local LM Studio, we don't need authorization header
        if api_key == 'not-needed':
            self.headers = {"Content-Type": "application/json"}
        else:
            self.headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
    
    def call_llm(self, prompt: str, system_prompt: str = None) -> str:
        """Make a call to the LLM API"""
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.3,
            "max_tokens": 1000
        }
        
        try:
            response = requests.post(self.endpoint + "/chat/completions", 
                                   headers=self.headers, 
                                   json=payload, 
                                   timeout=30)
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            print(f"Error calling LLM: {e}")
            return ""

class FunnelQuestionProcessor:
    def __init__(self, llm_client: LLMClient, business_context: str, categories: List[str]):
        self.llm_client = llm_client
        self.business_context = business_context
        self.categories = categories
    
    def evaluate_question_suitability(self, question: str) -> Tuple[bool, str, str]:
        """
        Evaluate if a question is suitable for funnel content creation
        Returns: (is_suitable, reasoning, article_concept)
        """
        system_prompt = f"""You are an expert B2B Content Strategist for AgentiveAIQ, a company that provides AI-powered chat agents and automation solutions for e-commerce businesses.

Business Context:
{self.business_context}

Your task is to analyze a question scraped from Google's "People Also Ask" and determine if it is suitable for creating a **Middle of the Funnel (MOFU)** or **Bottom of the Funnel (BOFU)** blog post that can attract potential customers.

**BE INCLUSIVE** - AgentiveAIQ serves e-commerce businesses with AI chat agents, so many e-commerce and customer service related questions are opportunities to create valuable content.

**ACCEPT (Suitable) - Questions that relate to:**
   * Chat support, live chat, chatbots, or AI agents (even definitional questions)
   * Customer service, customer support, or customer experience
   * E-commerce operations, conversion optimization, sales automation
   * Cart abandonment, lead generation, or customer engagement
   * E-commerce platforms (Shopify, WooCommerce) and integrations
   * Business automation, efficiency, or scaling customer interactions
   * Questions where we can position AgentiveAIQ as a solution

**REJECT (Not Suitable) - Only reject if:**
   * Completely unrelated to e-commerce, customer service, or business operations
   * Purely academic questions with no business application
   * Questions clearly for job seekers about employment (not business owners asking about services)
   * Questions about completely different industries (manufacturing processes, medical procedures, etc.)

**Examples:**
- SUITABLE: "What is e-commerce chat support?" → Perfect for explaining our core service
- SUITABLE: "How to reduce cart abandonment?" → We can position our proactive chat as the solution
- SUITABLE: "Is live chat customer service?" → Great opportunity to educate about AI chat agents
- SUITABLE: "What is e-commerce support?" → Broad but we can focus on chat/AI support aspects
- NOT SUITABLE: "What are the 5 C's of ecommerce?" → Too academic, no clear connection to our solutions
- NOT SUITABLE: "How to manufacture products?" → Unrelated to customer service/chat

**Remember:** If there's any reasonable way to connect the question to AgentiveAIQ's services, mark it as suitable.

Respond in this exact JSON format:
{{
    "is_suitable": true/false,
    "reasoning": "Brief explanation of why this question is or isn't suitable for funnel content",
    "article_concept": "If suitable, describe the article concept, angle, and how it would serve the funnel. If not suitable, write 'N/A'"
}}"""

        prompt = f"Evaluate this question for funnel content suitability: '{question}'"
        
        response = self.llm_client.call_llm(prompt, system_prompt)
        
        try:
            # Parse JSON response
            result = json.loads(response)
            return result["is_suitable"], result["reasoning"], result["article_concept"]
        except:
            # Fallback parsing if JSON fails
            return False, "Failed to parse LLM response", "N/A"
    
    def categorize_question(self, question: str, article_concept: str) -> str:
        """
        Categorize a suitable question into one of the predefined categories
        """
        categories_list = "\n".join([f"- {cat}" for cat in self.categories])
        
        system_prompt = f"""You are a content categorization expert for AgentiveAIQ.

Available Categories:
{categories_list}

Your task is to match the given question and article concept to exactly ONE category from the list above.

Respond with ONLY the category name exactly as it appears in the list. Do not add explanations or additional text."""

        prompt = f"""Question: '{question}'
Article Concept: '{article_concept}'

Which single category from the list best fits this content?"""
        
        response = self.llm_client.call_llm(prompt, system_prompt).strip()
        
        # Validate the response is in our categories list
        if response in self.categories:
            return response
        else:
            # Try to find a close match
            for category in self.categories:
                if category.lower() in response.lower() or response.lower() in category.lower():
                    return category
            return "Uncategorized"

def load_questions(file_path: str) -> List[str]:
    """Load questions from CSV file"""
    questions = []
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            if row and row[0].strip():  # Skip empty rows
                questions.append(row[0].strip())
    return questions

def load_business_context(file_path: str) -> str:
    """Load business context from text file"""
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.read()

def load_categories(file_path: str) -> List[str]:
    """Load categories from CSV file"""
    categories = []
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            if row and row[0].strip():
                categories.append(row[0].strip())
    return categories

def save_results(results: List[ProcessedQuestion], output_file: str):
    """Save processed results to CSV"""
    with open(output_file, 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['Original Question', 'Is Suitable', 'Reasoning', 'Article Concept', 'Category'])

        for result in results:
            writer.writerow([
                result.original_question,
                'Yes' if result.is_suitable else 'No',
                result.reasoning,
                result.article_concept,
                result.category or 'N/A'
            ])

def save_checkpoint(results: List[ProcessedQuestion], current_index: int, checkpoint_file: str):
    """Save current progress to checkpoint file"""
    checkpoint_data = {
        'results': results,
        'current_index': current_index,
        'timestamp': time.time()
    }
    with open(checkpoint_file, 'wb') as f:
        pickle.dump(checkpoint_data, f)

def load_checkpoint(checkpoint_file: str) -> Tuple[List[ProcessedQuestion], int]:
    """Load progress from checkpoint file"""
    try:
        with open(checkpoint_file, 'rb') as f:
            checkpoint_data = pickle.load(f)
        return checkpoint_data['results'], checkpoint_data['current_index']
    except (FileNotFoundError, KeyError, pickle.PickleError):
        return [], 0

def main():
    parser = argparse.ArgumentParser(description='Process bottom funnel questions')
    parser.add_argument('--test', action='store_true', help='Run in test mode')
    parser.add_argument('--test-count', type=int, default=10, help='Number of questions to process in test mode')
    parser.add_argument('--resume', action='store_true', help='Resume from checkpoint')
    parser.add_argument('--local', action='store_true', help='Use local LM Studio model')
    args = parser.parse_args()

    # Load configuration - default to local LM Studio if --local flag is used
    if args.local:
        llm_endpoint = 'http://*************:1234/v1'
        llm_model = 'qwen3-4b-instruct-2507'
        llm_api_key = 'not-needed'  # LM Studio doesn't require API key
    else:
        llm_endpoint = os.getenv('LLM_ENDPOINT', 'https://llm.chutes.ai/v1')
        llm_model = os.getenv('LLM_MODEL', 'Qwen/Qwen3-235B-A22B-Instruct-2507')
        llm_api_key = os.getenv('LLM_API_KEY', 'cpk_bd1d37cd6978483584aaa12d47312bb4.28b956be4459531ca85abf7422c1572b.KcFjkwzarUI5nrvvX9FMVmOubx2LHwm4')
    
    # Initialize LLM client
    llm_client = LLMClient(llm_endpoint, llm_model, llm_api_key)
    
    # Load data files
    print("Loading data files...")
    questions = load_questions('Bottom Funnel Questions - Sheet1.csv')
    business_context = load_business_context('business_context.txt')
    categories = load_categories('Categories - Sheet1.csv')
    
    print(f"Loaded {len(questions)} questions")
    print(f"Loaded {len(categories)} categories")
    
    # Initialize processor
    processor = FunnelQuestionProcessor(llm_client, business_context, categories)
    
    # Determine how many questions to process
    if args.test:
        questions_to_process = questions[:args.test_count]
        output_file = f'test_results_{args.test_count}.csv'
        checkpoint_file = f'checkpoint_test_{args.test_count}.pkl'
        print(f"Running in test mode - processing {len(questions_to_process)} questions")
    else:
        questions_to_process = questions
        output_file = 'processed_funnel_questions.csv'
        checkpoint_file = 'checkpoint_full.pkl'
        print(f"Processing all {len(questions_to_process)} questions")

    # Load checkpoint if resuming
    if args.resume:
        results, start_index = load_checkpoint(checkpoint_file)
        print(f"Resuming from checkpoint - already processed {start_index} questions")
        if start_index >= len(questions_to_process):
            print("All questions already processed!")
            return
    else:
        results = []
        start_index = 0

    for i in range(start_index, len(questions_to_process)):
        question = questions_to_process[i]
        current_num = i + 1
        print(f"Processing question {current_num}/{len(questions_to_process)}: {question[:50]}...")

        try:
            # Step 1: Evaluate suitability
            is_suitable, reasoning, article_concept = processor.evaluate_question_suitability(question)

            result = ProcessedQuestion(
                original_question=question,
                is_suitable=is_suitable,
                reasoning=reasoning,
                article_concept=article_concept
            )

            # Step 2: If suitable, categorize
            if is_suitable:
                print(f"  ✓ Suitable - categorizing...")
                category = processor.categorize_question(question, article_concept)
                result.category = category
                print(f"  → Category: {category}")
            else:
                print(f"  ✗ Not suitable")

            results.append(result)

            # Save checkpoint every 10 questions
            if (i + 1) % 10 == 0:
                save_checkpoint(results, i + 1, checkpoint_file)
                print(f"  💾 Checkpoint saved at question {i + 1}")

            # Add small delay to avoid overwhelming the API
            time.sleep(0.5)

        except Exception as e:
            print(f"  ❌ Error processing question: {e}")
            # Save checkpoint on error
            save_checkpoint(results, i, checkpoint_file)
            print(f"  💾 Emergency checkpoint saved. You can resume with --resume flag")
            raise
    
    # Save results
    save_results(results, output_file)
    
    # Print summary
    suitable_count = sum(1 for r in results if r.is_suitable)
    print(f"\n=== PROCESSING COMPLETE ===")
    print(f"Total questions processed: {len(results)}")
    print(f"Suitable for funnel content: {suitable_count}")
    print(f"Not suitable: {len(results) - suitable_count}")
    print(f"Results saved to: {output_file}")
    
    if suitable_count > 0:
        print(f"\nCategory distribution:")
        category_counts = {}
        for result in results:
            if result.is_suitable and result.category:
                category_counts[result.category] = category_counts.get(result.category, 0) + 1
        
        for category, count in sorted(category_counts.items()):
            print(f"  {category}: {count}")

if __name__ == "__main__":
    main()
